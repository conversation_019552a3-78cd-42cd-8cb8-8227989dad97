import { StyleSheet } from 'react-native';
import { Fonts } from '../utils/fonts';

/**
 * Global styles using Inter font family
 * These styles can be imported and used throughout the app
 */
export const GlobalStyles = StyleSheet.create({
  // Typography styles
  heading1: {
    fontFamily: Fonts.Inter.Bold,
    fontSize: 32,
    lineHeight: 40,
  },
  heading2: {
    fontFamily: Fonts.Inter.Bold,
    fontSize: 28,
    lineHeight: 36,
  },
  heading3: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 24,
    lineHeight: 32,
  },
  heading4: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 20,
    lineHeight: 28,
  },
  heading5: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 18,
    lineHeight: 26,
  },
  heading6: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 16,
    lineHeight: 24,
  },
  
  // Body text styles
  bodyLarge: {
    fontFamily: Fonts.Inter.Regular,
    fontSize: 18,
    lineHeight: 28,
  },
  body: {
    fontFamily: Fonts.Inter.Regular,
    fontSize: 16,
    lineHeight: 24,
  },
  bodySmall: {
    fontFamily: Fonts.Inter.Regular,
    fontSize: 14,
    lineHeight: 20,
  },
  
  // Medium weight variants
  bodyLargeMedium: {
    fontFamily: Fonts.Inter.Medium,
    fontSize: 18,
    lineHeight: 28,
  },
  bodyMedium: {
    fontFamily: Fonts.Inter.Medium,
    fontSize: 16,
    lineHeight: 24,
  },
  bodySmallMedium: {
    fontFamily: Fonts.Inter.Medium,
    fontSize: 14,
    lineHeight: 20,
  },
  
  // Caption and small text
  caption: {
    fontFamily: Fonts.Inter.Regular,
    fontSize: 12,
    lineHeight: 16,
  },
  captionMedium: {
    fontFamily: Fonts.Inter.Medium,
    fontSize: 12,
    lineHeight: 16,
  },
  overline: {
    fontFamily: Fonts.Inter.Medium,
    fontSize: 10,
    lineHeight: 16,
    letterSpacing: 1.5,
    textTransform: 'uppercase',
  },
  
  // Button styles
  buttonLarge: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 18,
    lineHeight: 24,
  },
  button: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 16,
    lineHeight: 24,
  },
  buttonSmall: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 14,
    lineHeight: 20,
  },
  
  // Link styles
  link: {
    fontFamily: Fonts.Inter.Medium,
    fontSize: 16,
    lineHeight: 24,
    textDecorationLine: 'underline',
  },
  linkSmall: {
    fontFamily: Fonts.Inter.Medium,
    fontSize: 14,
    lineHeight: 20,
    textDecorationLine: 'underline',
  },
  
  // Common layout styles
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // Common spacing
  marginSmall: {
    margin: 8,
  },
  marginMedium: {
    margin: 16,
  },
  marginLarge: {
    margin: 24,
  },
  
  paddingSmall: {
    padding: 8,
  },
  paddingMedium: {
    padding: 16,
  },
  paddingLarge: {
    padding: 24,
  },
});

/**
 * Color constants that work well with Inter font
 */
export const Colors = {
  primary: '#6B46C1',
  primaryLight: '#8B5CF6',
  primaryDark: '#553C9A',
  
  secondary: '#10B981',
  secondaryLight: '#34D399',
  secondaryDark: '#059669',
  
  text: {
    primary: '#1F2937',
    secondary: '#6B7280',
    tertiary: '#9CA3AF',
    inverse: '#FFFFFF',
  },
  
  background: {
    primary: '#FFFFFF',
    secondary: '#F9FAFB',
    tertiary: '#F3F4F6',
  },
  
  border: {
    light: '#E5E7EB',
    medium: '#D1D5DB',
    dark: '#9CA3AF',
  },
  
  status: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
} as const;
