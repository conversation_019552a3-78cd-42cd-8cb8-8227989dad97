# Inter Font Configuration

This project is configured to use the Inter font family, a modern typeface designed specifically for user interfaces and digital screens.

## Font Files

The following Inter font files are included in the project:

- `Inter-Regular.ttf` - Regular weight (400)
- `Inter-Medium.ttf` - Medium weight (500)
- `Inter-SemiBold.ttf` - Semi-bold weight (600)
- `Inter-Bold.ttf` - Bold weight (700)
- `Inter-Italic.ttf` - Italic variant

## Usage

### Using Font Utilities

The project includes font utilities to make it easy to use Inter fonts consistently:

```typescript
import { Fonts, getInterFont, FontStyles } from '../utils/fonts';

// Direct font family names
const styles = StyleSheet.create({
  title: {
    fontFamily: Fonts.Inter.Bold,
    fontSize: 24,
  },
  body: {
    fontFamily: Fonts.Inter.Regular,
    fontSize: 16,
  },
});

// Using helper function
const styles = StyleSheet.create({
  heading: {
    fontFamily: getInterFont('bold'),
    fontSize: 20,
  },
  italic: {
    fontFamily: getInterFont('regular', true), // italic
    fontSize: 16,
  },
});

// Using predefined styles
const styles = StyleSheet.create({
  title: {
    ...FontStyles.heading2,
    color: '#333',
  },
});
```

### Using Global Styles

For consistency across the app, use the global styles:

```typescript
import { GlobalStyles, Colors } from '../styles/globalStyles';

const MyComponent = () => (
  <View>
    <Text style={[GlobalStyles.heading1, { color: Colors.text.primary }]}>
      Main Title
    </Text>
    <Text style={[GlobalStyles.body, { color: Colors.text.secondary }]}>
      Body text content
    </Text>
  </View>
);
```

## Font Weights and Styles

### Available Weights
- **Regular (400)**: `Inter-Regular` - For body text and general content
- **Medium (500)**: `Inter-Medium` - For emphasized text and labels
- **Semi-Bold (600)**: `Inter-SemiBold` - For buttons and important text
- **Bold (700)**: `Inter-Bold` - For headings and titles

### Style Guidelines

1. **Headings**: Use Bold or Semi-Bold weights
2. **Body Text**: Use Regular weight
3. **Buttons**: Use Semi-Bold weight
4. **Labels**: Use Medium weight
5. **Captions**: Use Regular weight with smaller font size

## Typography Scale

The project uses a consistent typography scale:

- **Heading 1**: 32px, Bold, 40px line height
- **Heading 2**: 28px, Bold, 36px line height
- **Heading 3**: 24px, Semi-Bold, 32px line height
- **Heading 4**: 20px, Semi-Bold, 28px line height
- **Body Large**: 18px, Regular, 28px line height
- **Body**: 16px, Regular, 24px line height
- **Body Small**: 14px, Regular, 20px line height
- **Caption**: 12px, Regular, 16px line height

## Best Practices

1. **Consistency**: Always use the font utilities or global styles instead of hardcoding font families
2. **Readability**: Maintain appropriate line heights (1.4-1.6x font size)
3. **Hierarchy**: Use font weights and sizes to create clear visual hierarchy
4. **Performance**: The fonts are preloaded in the app layout for optimal performance

## Troubleshooting

If fonts are not displaying correctly:

1. Ensure the font files are in `assets/fonts/`
2. Check that fonts are loaded in `app/_layout.tsx`
3. Verify font names match exactly (case-sensitive)
4. Clear Metro cache: `npx expo start --clear`

## Adding New Font Weights

To add additional Inter font weights:

1. Download the font file from [Inter GitHub repository](https://github.com/rsms/inter)
2. Add the file to `assets/fonts/`
3. Update the font loading in `app/_layout.tsx`
4. Add the font name to `utils/fonts.ts`
5. Update this documentation
