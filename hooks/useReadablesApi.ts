import { useCallback, useState } from 'react';
import type { LoginPayload, VerifyLoginPayloadParams } from "thirdweb/auth";

// Types for API responses
interface LoginResponse {
  success: boolean;
  token?: string;
  user?: any;
  message?: string;
}

interface LogoutResponse {
  success: boolean;
  message?: string;
}

interface IsLoggedInResponse {
  isLoggedIn: boolean;
  user?: any;
  message?: string;
}

// API Error class for better error handling
class ReadablesApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ReadablesApiError';
  }
}

// Hook for managing Readables API calls
export const useReadablesApi = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get base URL and auth credentials from environment variables
  const baseUrl = process.env.EXPO_PUBLIC_API_SERVER_URL;
  const authUser = process.env.EXPO_PUBLIC_API_SERVER_USER;
  const authPassword = process.env.EXPO_PUBLIC_API_SERVER_PASSWORD;

  if (!baseUrl) {
    console.warn('EXPO_PUBLIC_API_SERVER_URL environment variable is not set');
  }

  // Create basic auth header if credentials are provided
  const getAuthHeaders = useCallback(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (authUser && authPassword) {
      const credentials = btoa(`${authUser}:${authPassword}`);
      headers['Authorization'] = `Basic ${credentials}`;
    }

    return headers;
  }, [authUser, authPassword]);

  // Helper function to make API requests
  const makeRequest = useCallback(async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    if (!baseUrl) {
      throw new ReadablesApiError('API base URL is not configured');
    }

    setIsLoading(true);
    setError(null);

    try {
      const url = `${baseUrl}${endpoint}`;
      const defaultHeaders = getAuthHeaders();
      const response = await fetch(url, {
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new ReadablesApiError(
          `API request failed: ${response.status} ${response.statusText}`,
          response.status,
          errorText
        );
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [baseUrl, getAuthHeaders]);

  // Check if user is logged in
  const isLoggedIn = useCallback(async (address?: string): Promise<IsLoggedInResponse> => {
    console.log("checking if logged in!", { address });

    const endpoint = address ? `/isloggedin?address=${address}` : '/isloggedin';
    return makeRequest<IsLoggedInResponse>(endpoint);
  }, [makeRequest]);

  // Login user
  const login = useCallback(async (params: VerifyLoginPayloadParams): Promise<LoginResponse> => {
    console.log("logging in!", { params });

    return makeRequest<LoginResponse>('/login', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }, [makeRequest]);

  // Get login payload
  const getLoginPayload = useCallback(async (params: {
    address: string;
    chainId: number;
  }): Promise<LoginPayload> => {
    console.log("getting login payload!", { params });

    const endpoint = `/login?address=${params.address}&chainId=${params.chainId}`;
    return makeRequest<LoginPayload>(endpoint);
  }, [makeRequest]);

  // Logout user
  const logout = useCallback(async (): Promise<LogoutResponse> => {
    console.log("logging out!");

    return makeRequest<LogoutResponse>('/logout', {
      method: 'POST',
    });
  }, [makeRequest]);

  return {
    // API methods
    isLoggedIn,
    login,
    getLoginPayload,
    logout,

    // State
    isLoading,
    error,

    // Utility
    clearError: () => setError(null),
    baseUrl,
    hasBasicAuth: !!(authUser && authPassword),
  };
};

export default useReadablesApi;
