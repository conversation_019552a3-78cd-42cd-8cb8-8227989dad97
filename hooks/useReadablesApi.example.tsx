import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Text, View } from 'react-native';
import { useReadablesApi } from './useReadablesApi';

/**
 * Example component demonstrating how to use the useReadablesApi hook
 * This is for demonstration purposes only
 */
export const ReadablesApiExample = () => {
  const {
    isLoggedIn,
    login,
    logout,
    getLoginPayload,
    isLoading,
    error,
    clearError,
    hasBasicAuth
  } = useReadablesApi();

  const handleCheckLogin = async () => {
    try {
      const result = await isLoggedIn('0x1234567890123456789012345678901234567890');
      Alert.alert('Login Status', `User is ${result.isLoggedIn ? 'logged in' : 'not logged in'}`);
    } catch (err) {
      Alert.alert('Error', 'Failed to check login status');
    }
  };

  const handleGetLoginPayload = async () => {
    try {
      const payload = await getLoginPayload({
        address: '0x1234567890123456789012345678901234567890',
        chainId: 137
      });
      Alert.alert('Success', `Got login payload: ${JSON.stringify(payload)}`);
    } catch (err) {
      Alert.alert('Error', 'Failed to get login payload');
    }
  };

  const handleLogin = async () => {
    try {
      // This would normally come from ThirdWeb's verification process
      const mockParams = {
        payload: {
          domain: 'example.com',
          address: '0x1234567890123456789012345678901234567890',
          statement: 'Sign in to Readables',
          uri: 'https://example.com',
          version: '1',
          chainId: 137,
          nonce: 'random-nonce',
          issuedAt: new Date().toISOString(),
        },
        signature: 'mock-signature'
      };

      await login(mockParams);
      Alert.alert('Success', 'Login successful!');
    } catch (err) {
      Alert.alert('Error', 'Login failed');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      Alert.alert('Success', 'Logout successful!');
    } catch (err) {
      Alert.alert('Error', 'Logout failed');
    }
  };

  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
        Readables API Hook Example
      </Text>

      <Text style={{ fontSize: 14, color: hasBasicAuth ? '#2e7d32' : '#f57c00', marginBottom: 20 }}>
        Basic Auth: {hasBasicAuth ? 'Configured ✓' : 'Not configured'}
      </Text>

      {error && (
        <View style={{ backgroundColor: '#ffebee', padding: 10, marginBottom: 10, borderRadius: 5 }}>
          <Text style={{ color: '#c62828' }}>Error: {error}</Text>
          <Button title="Clear Error" onPress={clearError} />
        </View>
      )}

      {isLoading && (
        <Text style={{ color: '#1976d2', marginBottom: 10 }}>Loading...</Text>
      )}

      <View style={{ gap: 10 }}>
        <Button title="Check Login Status" onPress={handleCheckLogin} disabled={isLoading} />
        <Button title="Get Login Payload" onPress={handleGetLoginPayload} disabled={isLoading} />
        <Button title="Login (Mock)" onPress={handleLogin} disabled={isLoading} />
        <Button title="Logout" onPress={handleLogout} disabled={isLoading} />
      </View>

      <Text style={{ marginTop: 20, fontSize: 12, color: '#666' }}>
        Note: Make sure EXPO_PUBLIC_API_SERVER_URL is set in your environment variables.
        {'\n'}If your API requires authentication, also set EXPO_PUBLIC_API_SERVER_USER and EXPO_PUBLIC_API_SERVER_PASSWORD.
      </Text>
    </View>
  );
};
