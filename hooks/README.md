# Readables API Hook

This directory contains React hooks for interacting with the Readables REST API.

## useReadablesApi Hook

The `useReadablesApi` hook provides a centralized way to make API calls to the Readables backend server.

### Setup

1. Set the required environment variables in your `.env` file:
   ```
   EXPO_PUBLIC_API_SERVER_URL=https://api.readables.ai
   ```

2. (Optional) If your API server requires basic authentication, also set:
   ```
   EXPO_PUBLIC_API_SERVER_USER=your_username
   EXPO_PUBLIC_API_SERVER_PASSWORD=your_password
   ```

3. Import and use the hook in your component:
   ```typescript
   import { useReadablesApi } from '../hooks/useReadablesApi';

   const MyComponent = () => {
     const api = useReadablesApi();

     // Use the API methods
   };
   ```

### Available Methods

#### `isLoggedIn(address?: string): Promise<IsLoggedInResponse>`
Checks if a user is currently logged in.

```typescript
const result = await api.isLoggedIn(userAddress);
console.log(result.isLoggedIn); // boolean
```

#### `login(params: VerifyLoginPayloadParams): Promise<LoginResponse>`
Logs in a user with the provided verification parameters.

```typescript
await api.login(loginParams);
```

#### `getLoginPayload(params: {address: string, chainId: number}): Promise<LoginPayload>`
Gets the login payload for a specific address and chain ID.

```typescript
const payload = await api.getLoginPayload({
  address: userAddress,
  chainId: 137
});
```

#### `logout(): Promise<LogoutResponse>`
Logs out the current user.

```typescript
await api.logout();
```

### State Management

The hook also provides loading and error states, plus authentication status:

```typescript
const { isLoading, error, clearError, hasBasicAuth } = useReadablesApi();

if (isLoading) {
  return <LoadingSpinner />;
}

if (error) {
  return <ErrorMessage message={error} onDismiss={clearError} />;
}

// Check if basic auth is configured
if (hasBasicAuth) {
  console.log('API requests will include basic authentication');
}
```

### Error Handling

All API methods include proper error handling. Errors are automatically caught and stored in the `error` state. You can also handle errors manually:

```typescript
try {
  await api.login(params);
} catch (error) {
  console.error('Login failed:', error);
}
```

### TypeScript Support

The hook is fully typed with TypeScript interfaces for all request and response types.

### Environment Variables

- `EXPO_PUBLIC_API_SERVER_URL`: The base URL for the Readables API server (required)
- `EXPO_PUBLIC_API_SERVER_USER`: Username for basic authentication (optional)
- `EXPO_PUBLIC_API_SERVER_PASSWORD`: Password for basic authentication (optional)

### Basic Authentication

The hook automatically detects if basic authentication credentials are provided via environment variables. If both `EXPO_PUBLIC_API_SERVER_USER` and `EXPO_PUBLIC_API_SERVER_PASSWORD` are set, all API requests will include the appropriate `Authorization: Basic` header.

If these variables are not set, requests will be made without authentication headers.
