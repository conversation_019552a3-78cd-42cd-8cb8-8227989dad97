{"name": "readables", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@coinbase/wallet-mobile-sdk": "^1.1.2", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/audio-toolkit": "^2.0.3", "@react-native-community/netinfo": "11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@thirdweb-dev/react-native-adapter": "^1.5.4", "@walletconnect/react-native-compat": "^2.20.3", "amazon-cognito-identity-js": "^6.3.15", "expo": "~53.0.9", "expo-application": "~6.1.4", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-aes-gcm-crypto": "^0.2.2", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-mmkv": "^3.2.0", "react-native-passkey": "^3.1.0", "react-native-quick-crypto": "^0.7.13", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-tts": "^4.1.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "thirdweb": "^5.101.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}