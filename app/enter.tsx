import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Image,
    ImageBackground,
    SafeAreaView,
    StyleSheet,
    Text,
    View
} from 'react-native';
import type { ThirdwebClient } from "thirdweb";
import type { LoginPayload, VerifyLoginPayloadParams } from "thirdweb/auth";
import { polygonAmoy } from "thirdweb/chains";
import { ConnectEmbed } from 'thirdweb/react';
import { inAppWallet } from "thirdweb/wallets";
import { useReadablesApi } from '../hooks/useReadablesApi';
import thirdWebClient from './thirdwebclient';

const Enter = () => {

  const [client, setClient] = useState<ThirdwebClient|null>(null);

  // Initialize the Readables API hook
  const api = useReadablesApi();

  const wallets = [
    inAppWallet({
      auth: {
        options: [
          "google",
          "email",
          "apple",
          "github",
          "x",
        ],
      },
    }),
  ];

  useEffect(() => {
      setClient(thirdWebClient);
  }, [thirdWebClient]);


  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.backgroundContainer}>

        <ImageBackground source={require('./../assets/images/waves_001.png')} resizeMode="cover" style={styles.image}>

          {/* Header Section */}
          <View style={styles.headerSection}>
            <View style={styles.logoContainer}>
              <Image
                source={require('../assets/images/Logo_light_Readables.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.logoText}>readables</Text>
            </View>
          </View>

          <Text style={styles.subtitle}>
            Join thousands of users who are already creating their readables
          </Text>

          <View style={styles.loginContainer}>
            <View style={styles.connectEmbed}>
            {client && <ConnectEmbed
              autoConnect={false}
              wallets={wallets}
              showThirdwebBranding={false}
              showAllWallets={false}
              chain={polygonAmoy}
              theme={"light"}
              client={client}
              auth={{
                isLoggedIn: async (address) => {
                  try {
                    const result = await api.isLoggedIn(address);
                    if (result.isLoggedIn) {
                      setTimeout(() => {
                        router.push('/(auth)/home');
                      }, 1000);
                    }
                    return result.isLoggedIn;
                  } catch (error) {
                    console.error("Login check failed:", error);
                    return false;
                  }
                },
                doLogin: async (params: VerifyLoginPayloadParams) => {
                  try {
                    await api.login(params);
                    setTimeout(() => {
                        router.push('/(auth)/home');
                    }, 1000);
                  } catch (error) {
                    console.error("Login failed:", error);
                    throw new Error("Login failed");
                  }
                },
                getLoginPayload: async (params: {address: string, chainId: number}): Promise<LoginPayload> => {
                  try {
                    return await api.getLoginPayload(params);
                  } catch (error) {
                    console.error("Get login payload failed:", error);
                    throw error;
                  }
                },
                doLogout: async () => {
                  try {
                    await api.logout();
                  } catch (error) {
                    console.error("Logout failed:", error);
                    throw error;
                  }
                },
              }} />}
              </View>
          </View>

        </ImageBackground>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  backgroundContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  loginContainer: {
    flex: 1,
  },
  connectEmbed: {
    flex: 0.8,
    backgroundColor: 'white',
    marginHorizontal: 24,
    paddingHorizontal: 24,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerSection: {
    paddingHorizontal: 24,
    paddingTop: 100,
    paddingBottom: 60,
    alignItems: 'center',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 60,
    height: 64,
  },
  logoText: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: 'white',
    alignContent: 'center',
    paddingBottom: 8,
  },
  mainTitle: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 34,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
    marginHorizontal: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  primaryButtonText: {
    color: '#6B46C1',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: 'white',
  },
  secondaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  waveContainer: {
    marginTop: -20,
  },
  howItWorksSection: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 60,
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 40,
  },
  image: {
    flex: 1,
    justifyContent: 'center',
    height: '100%',
  },
  iconContainer: {
    width: 80,
    height: 80,
    backgroundColor: '#F3F4F6',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
});

export default Enter;
