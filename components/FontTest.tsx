import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Fonts, getInterFont } from '../utils/fonts';
import { GlobalStyles, Colors } from '../styles/globalStyles';

/**
 * FontTest component to verify Inter fonts are loaded correctly
 * This component can be used during development to test font rendering
 */
export const FontTest: React.FC = () => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Inter Font Test</Text>
        
        {/* Direct font family usage */}
        <View style={styles.group}>
          <Text style={styles.groupTitle}>Direct Font Families</Text>
          <Text style={[styles.sample, { fontFamily: Fonts.Inter.Regular }]}>
            Inter Regular - The quick brown fox jumps over the lazy dog
          </Text>
          <Text style={[styles.sample, { fontFamily: Fonts.Inter.Medium }]}>
            Inter Medium - The quick brown fox jumps over the lazy dog
          </Text>
          <Text style={[styles.sample, { fontFamily: Fonts.Inter.SemiBold }]}>
            Inter SemiBold - The quick brown fox jumps over the lazy dog
          </Text>
          <Text style={[styles.sample, { fontFamily: Fonts.Inter.Bold }]}>
            Inter Bold - The quick brown fox jumps over the lazy dog
          </Text>
          <Text style={[styles.sample, { fontFamily: Fonts.Inter.Italic }]}>
            Inter Italic - The quick brown fox jumps over the lazy dog
          </Text>
        </View>

        {/* Helper function usage */}
        <View style={styles.group}>
          <Text style={styles.groupTitle}>Helper Function Usage</Text>
          <Text style={[styles.sample, { fontFamily: getInterFont('regular') }]}>
            getInterFont('regular') - Regular weight
          </Text>
          <Text style={[styles.sample, { fontFamily: getInterFont('medium') }]}>
            getInterFont('medium') - Medium weight
          </Text>
          <Text style={[styles.sample, { fontFamily: getInterFont('semibold') }]}>
            getInterFont('semibold') - SemiBold weight
          </Text>
          <Text style={[styles.sample, { fontFamily: getInterFont('bold') }]}>
            getInterFont('bold') - Bold weight
          </Text>
          <Text style={[styles.sample, { fontFamily: getInterFont('regular', true) }]}>
            getInterFont('regular', true) - Italic variant
          </Text>
        </View>

        {/* Global styles usage */}
        <View style={styles.group}>
          <Text style={styles.groupTitle}>Global Styles</Text>
          <Text style={[GlobalStyles.heading1, { color: Colors.text.primary }]}>
            Heading 1 - Main Title
          </Text>
          <Text style={[GlobalStyles.heading2, { color: Colors.text.primary }]}>
            Heading 2 - Section Title
          </Text>
          <Text style={[GlobalStyles.heading3, { color: Colors.text.primary }]}>
            Heading 3 - Subsection
          </Text>
          <Text style={[GlobalStyles.body, { color: Colors.text.secondary }]}>
            Body text - This is regular body text using Inter Regular font.
          </Text>
          <Text style={[GlobalStyles.bodyMedium, { color: Colors.text.secondary }]}>
            Body Medium - This is emphasized body text using Inter Medium font.
          </Text>
          <Text style={[GlobalStyles.caption, { color: Colors.text.tertiary }]}>
            Caption - Small text for additional information
          </Text>
        </View>

        {/* Typography scale */}
        <View style={styles.group}>
          <Text style={styles.groupTitle}>Typography Scale</Text>
          <Text style={[GlobalStyles.heading1, { color: Colors.text.primary }]}>
            32px Heading 1
          </Text>
          <Text style={[GlobalStyles.heading2, { color: Colors.text.primary }]}>
            28px Heading 2
          </Text>
          <Text style={[GlobalStyles.heading3, { color: Colors.text.primary }]}>
            24px Heading 3
          </Text>
          <Text style={[GlobalStyles.heading4, { color: Colors.text.primary }]}>
            20px Heading 4
          </Text>
          <Text style={[GlobalStyles.bodyLarge, { color: Colors.text.secondary }]}>
            18px Body Large
          </Text>
          <Text style={[GlobalStyles.body, { color: Colors.text.secondary }]}>
            16px Body Regular
          </Text>
          <Text style={[GlobalStyles.bodySmall, { color: Colors.text.secondary }]}>
            14px Body Small
          </Text>
          <Text style={[GlobalStyles.caption, { color: Colors.text.tertiary }]}>
            12px Caption
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: Fonts.Inter.Bold,
    color: Colors.text.primary,
    marginBottom: 20,
    textAlign: 'center',
  },
  group: {
    marginBottom: 30,
    padding: 15,
    backgroundColor: Colors.background.secondary,
    borderRadius: 8,
  },
  groupTitle: {
    fontSize: 18,
    fontFamily: Fonts.Inter.SemiBold,
    color: Colors.text.primary,
    marginBottom: 15,
  },
  sample: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginBottom: 8,
    lineHeight: 24,
  },
});
