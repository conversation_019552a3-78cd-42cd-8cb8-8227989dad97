/**
 * Font utility for Inter font family
 * Provides consistent font family names and helper functions
 */

export const Fonts = {
  Inter: {
    Regular: 'Inter-Regular',
    Medium: 'Inter-Medium',
    SemiBold: 'Inter-SemiBold',
    Bold: 'Inter-Bold',
    Italic: 'Inter-Italic',
  },
} as const;

/**
 * Helper function to get Inter font family based on weight
 * @param weight - Font weight: 'regular' | 'medium' | 'semibold' | 'bold'
 * @param italic - Whether to use italic variant
 * @returns Font family name
 */
export const getInterFont = (
  weight: 'regular' | 'medium' | 'semibold' | 'bold' = 'regular',
  italic: boolean = false
): string => {
  if (italic) {
    return Fonts.Inter.Italic;
  }

  switch (weight) {
    case 'medium':
      return Fonts.Inter.Medium;
    case 'semibold':
      return Fonts.Inter.SemiBold;
    case 'bold':
      return Fonts.Inter.Bold;
    case 'regular':
    default:
      return Fonts.Inter.Regular;
  }
};

/**
 * Common font styles using Inter
 */
export const FontStyles = {
  heading1: {
    fontFamily: Fonts.Inter.Bold,
    fontSize: 32,
    lineHeight: 40,
  },
  heading2: {
    fontFamily: Fonts.Inter.Bold,
    fontSize: 28,
    lineHeight: 36,
  },
  heading3: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 24,
    lineHeight: 32,
  },
  heading4: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 20,
    lineHeight: 28,
  },
  body: {
    fontFamily: Fonts.Inter.Regular,
    fontSize: 16,
    lineHeight: 24,
  },
  bodyMedium: {
    fontFamily: Fonts.Inter.Medium,
    fontSize: 16,
    lineHeight: 24,
  },
  caption: {
    fontFamily: Fonts.Inter.Regular,
    fontSize: 14,
    lineHeight: 20,
  },
  button: {
    fontFamily: Fonts.Inter.SemiBold,
    fontSize: 16,
    lineHeight: 24,
  },
  small: {
    fontFamily: Fonts.Inter.Regular,
    fontSize: 12,
    lineHeight: 16,
  },
};
